{"version": 2, "name": "tradai-signal-generator", "framework": "nextjs", "buildCommand": "npm run vercel-build", "devCommand": "npm run dev", "installCommand": "npm install", "env": {"NODE_ENV": "production", "VERCEL": "1", "OPENCV4NODEJS_DISABLE_AUTOBUILD": "1"}, "functions": {"pages/api/forex-signal-generator.ts": {"maxDuration": 300}, "pages/api/professional-chart-analysis.js": {"maxDuration": 300}, "pages/api/vercel-health.js": {"maxDuration": 10}}, "regions": ["iad1"], "routes": [{"src": "/api/professional-chart-analysis", "dest": "/api/professional-chart-analysis"}, {"src": "/otc-generator", "dest": "/otc-generator"}, {"src": "/api/forex-signal-generator", "dest": "/api/forex-signal-generator"}, {"src": "/forex-signal-generator", "dest": "/forex-signal-generator"}, {"src": "/api/health", "dest": "/api/vercel-health"}]}